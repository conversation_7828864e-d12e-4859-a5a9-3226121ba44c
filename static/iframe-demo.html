<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe通信演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .message-log {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .message-item {
            margin-bottom: 8px;
            padding: 5px;
            border-left: 3px solid #4CAF50;
            padding-left: 10px;
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            box-sizing: border-box;
        }
        
        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 iframe通信演示页面</h1>
            <p>这是一个用于演示与uni-app通信的测试页面</p>
        </div>
        
        <div class="card">
            <h3>📤 发送消息到父页面</h3>
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="输入要发送的消息...">
            </div>
            <button class="btn" onclick="sendMessageToParent()">发送消息</button>
            <button class="btn" onclick="sendUserInfo()">发送用户信息</button>
            <button class="btn" onclick="sendRandomData()">发送随机数据</button>
        </div>
        
        <div class="card">
            <h3>📥 接收到的消息</h3>
            <div id="messageLog" class="message-log">
                <div class="message-item">等待接收消息...</div>
            </div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="card">
            <h3>ℹ️ 页面信息</h3>
            <p><strong>当前时间:</strong> <span id="currentTime"></span></p>
            <p><strong>页面URL:</strong> <span id="pageUrl"></span></p>
            <p><strong>用户代理:</strong> <span id="userAgent"></span></p>
        </div>
    </div>

    <script>
        // 更新页面信息
        function updatePageInfo() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
            document.getElementById('pageUrl').textContent = window.location.href;
            document.getElementById('userAgent').textContent = navigator.userAgent;
        }
        
        // 添加日志消息
        function addLogMessage(message, type = 'info') {
            const logContainer = document.getElementById('messageLog');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-item';
            messageDiv.innerHTML = `
                <strong>[${new Date().toLocaleTimeString()}]</strong> 
                ${typeof message === 'object' ? JSON.stringify(message, null, 2) : message}
            `;
            
            // 如果是第一条消息，清空默认提示
            if (logContainer.children.length === 1 && logContainer.children[0].textContent === '等待接收消息...') {
                logContainer.innerHTML = '';
            }
            
            logContainer.insertBefore(messageDiv, logContainer.firstChild);
            
            // 限制消息数量
            if (logContainer.children.length > 20) {
                logContainer.removeChild(logContainer.lastChild);
            }
        }
        
        // 发送消息到父页面
        function sendMessageToParent() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim() || 'Hello from iframe!';
            
            const data = {
                type: 'custom_message',
                data: {
                    message: message,
                    timestamp: Date.now(),
                    source: 'iframe-demo'
                }
            };
            
            // 发送消息到父页面
            if (window.parent && window.parent !== window) {
                window.parent.postMessage(data, '*');
                addLogMessage(`发送消息: ${message}`);
                input.value = '';
            } else {
                addLogMessage('错误: 无法找到父页面');
            }
        }
        
        // 发送用户信息
        function sendUserInfo() {
            const userInfo = {
                type: 'user_info',
                data: {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    platform: navigator.platform,
                    screenWidth: screen.width,
                    screenHeight: screen.height,
                    timestamp: Date.now()
                }
            };
            
            if (window.parent && window.parent !== window) {
                window.parent.postMessage(userInfo, '*');
                addLogMessage('发送用户信息');
            }
        }
        
        // 发送随机数据
        function sendRandomData() {
            const randomData = {
                type: 'random_data',
                data: {
                    randomNumber: Math.floor(Math.random() * 1000),
                    randomString: Math.random().toString(36).substring(7),
                    timestamp: Date.now()
                }
            };
            
            if (window.parent && window.parent !== window) {
                window.parent.postMessage(randomData, '*');
                addLogMessage('发送随机数据');
            }
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('messageLog').innerHTML = '<div class="message-item">日志已清空</div>';
        }
        
        // 监听来自父页面的消息
        window.addEventListener('message', function(event) {
            console.log('收到父页面消息:', event.data);
            addLogMessage(`收到父页面消息: ${JSON.stringify(event.data)}`);
            
            // 根据消息类型进行响应
            if (event.data && event.data.type === 'dataRequest') {
                // 响应数据请求
                const responseData = {
                    type: 'dataResponse',
                    data: {
                        requestId: event.data.data.requestId,
                        responseData: {
                            status: 'success',
                            userData: {
                                name: 'iframe用户',
                                id: 12345,
                                timestamp: Date.now()
                            }
                        }
                    }
                };
                
                window.parent.postMessage(responseData, '*');
                addLogMessage('已响应数据请求');
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePageInfo();
            addLogMessage('iframe页面加载完成');
            
            // 定时更新时间
            setInterval(updatePageInfo, 1000);
            
            // 监听回车键发送消息
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessageToParent();
                }
            });
        });
    </script>
</body>
</html>
