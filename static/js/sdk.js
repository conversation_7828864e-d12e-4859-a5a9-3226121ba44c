
function sdkPay(name) {
  window.location.href = 'http://qmsdkh5.qmgames.cn/#/pages/payment/payment?name=' + name
}
function sdkInit(name) {
  window.location.href = 'http://qmsdkh5.qmgames.cn/#/pages/login/login?initData=' + name
}

function sdkRole(data) {

	fetch('https://devsdkapi.7dgame.cn/v1.role/index.html?api_debug=true', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'token': 'J3YrYmrn7cOLBj4pY2jiti+qIQ/2HC/v02nvocivvxynA8p6ex3E4de04bYwVG8mqrQSDDqgVNkqhQl4UCYnlfqXqUpjipU'
		},
		body: JSON.stringify(data)
	})
	.then(response => {
	console.log(response);
		return response.json();  // 解析JSON数据
	})
	.then(data => {
		console.log('Success:', data);  // 处理成功响应
	})
	.catch(error => {
		console.error('Error:', error);  // 处理错误响应
	});
}

