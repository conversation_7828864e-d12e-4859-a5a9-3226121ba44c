{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/certification/certification",
			"style": {
				"navigationBarTitleText": "实名认证",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/order/order-details",
			"style": {
				"navigationBarTitleText": "账单详情",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/coupon/my-coupon",
			"style": {
				"navigationBarTitleText": "我的代金券",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/recharge/recharge-details",
			"style": {
				"navigationBarTitleText": "交易明细",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/index/edition",
			"style": {
				"navigationBarTitleText": "游戏版本消息",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/index/password",
			"style": {
				"navigationBarTitleText": "修改密码",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/index/phone",
			"style": {
				"navigationBarTitleText": "绑定手机",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/index/real",
			"style": {
				"navigationBarTitleText": "实名认证",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/index/service",
			"style": {
				"navigationBarTitleText": "客服中心",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/index/agreement",
			"style": {
				"navigationBarTitleText": "协议",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/gift/my-gift",
			"style": {
				"navigationBarTitleText": "我的礼包",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/payment/payment",
			"style": {
				"navigationBarTitleText": "支付",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path": "pages/game/game",
			"style": {
				"navigationBarTitleText": "游戏",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"

			}
		},
		{
			"path": "pages/demo/demo",
			"style": {
				"navigationBarTitleText": "Demo演示",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"

			}
		}
		
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#F8F8F8" //背景色
	},

	"tabBar": {
		"backgroundColor": "#fff",
		"color": "#9799a5",
		"selectedColor": "#05D4BB",
		"borderStyle": "black",
		"list": [{
			"text": "推荐",
			"pagePath": "pages/index/index1",
			"iconPath": "static/images/tabBar/home.png",
			"selectedIconPath": "static/images/tabBar/home-h.png"
		}, {
			"text": "找游戏",
			"pagePath": "pages/cateGame/cateGame",
			"iconPath": "static/images/tabBar/game.png",
			"selectedIconPath": "static/images/tabBar/game-h.png"
		}]
	},

	"uniIdRouter": {},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}

}