<template>
	<view class="mask"  v-if="isshow">
		<view class="content">
			<view class="back" @click="togame">
				<uni-icons class="icon" type="left" size="22" color="#999" @click="navigateBack"></uni-icons>
				<text class="font12 color999">回到游戏</text>
			</view>
			<view class="user_logo">
				<image src="../../static/images/user/qimeng_ic_defaule_head.png" mode="" class="logo"></image>
				<view class="name">
					<view class="user_name"> {{userinfo.username}} </view>
					<view class="user_money">
						<image src="../../static/images/user/qimeng_ic_platform_money.png" mode=""></image>
						<text>余额：0.0</text>
					</view>
				</view>
			</view>
			<view class="tabs">
				<view class="tab" v-for="(tab, index) in img_list" :key="index"
					:class="{ active: activeIndex === index }" @click="listClick(index)">
					<image v-if="activeIndex == index" :src="tab.logo" mode="" class="img"></image>
					<image v-if="activeIndex != index" :src="tab.logo1" mode="" class="img"></image>
					<view class="names" :class="{ nameActive: activeIndex != index }">{{
            tab.name
          }}</view>
				</view>
			</view>
			<view class="line"></view>
			<!-- 我的 -->
			<view class="" v-if="activeIndex == 0">
				
				<view class="mytitle">
					<view class="myleft"></view>
					<view class="myright">我的</view>
				</view>
				<scroll-view scroll-y="true">
					<view class="sview">
				<view class="mylist" @click="onClickItem('edition')">
					<view class="list_left">游戏版本更新</view>
					<view class="list_right">
						<text>当前版本 v3.3.8</text>
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('password')">
					<view class="list_left">修改密码</view>
					<view class="list_right">
						<!-- <text>131****7501 </text> -->
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('phone')">
					<view class="list_left">绑定手机</view>
					<view class="list_right">
						<text> {{userinfo.mobile}} </text>
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('real')">
					<view class="list_left">实名认证</view>
					<view class="list_right">
						<text>已认证</text>
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('service')">
					<view class="list_left">客服中心</view>
					<view class="list_right">
						<!-- <text>当前版本 v3.3.8</text> -->
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('download')">
					<view class="list_left" style="color: #f7a468;">下载APP每天领3元代金券</view>
					<view class="list_right">
						<!-- <text>当前版本 v3.3.8</text> -->
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('agreement')">
					<view class="list_left">协议</view>
					<view class="list_right">
						<!-- <text>当前版本 v3.3.8</text> -->
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				<view class="mylist" @click="onClickItem('loginout')">
					<view class="list_left">切换账号</view>
					<view class="list_right">
						<!-- <text>当前版本 v3.3.8</text> -->
						<uni-icons class="icon" type="right" size="18" color="#999" @click="navigateBack"></uni-icons>
					</view>
				</view>
				</view>
				</scroll-view>
			</view>
			<!-- 订单 -->
			<view class="" v-if="activeIndex == 1" >
				<orderList></orderList>
				<!-- <agreePop ref="childpop"></agreePop> -->
			</view>
			<view class="" v-if="activeIndex == 2">
				<recharge></recharge>
			</view>
			<view class="" v-if="activeIndex == 3">
				<coupon></coupon> 
			</view>
			<view class="" v-if="activeIndex == 4">
				<gift></gift>
			</view>
		</view>	
	</view>
	<outPop ref="childPop" @outClick="outClick" :name="userinfo.username"></outPop>	
	<ball @change-parent-param="updateParentParam"></ball>
	<edition v-if="isshow_edition == true" @goback="goback"></edition>
	<password v-if="isshow_password == true" @goback="goback"></password> 
	<service v-if="isshow_service == true" @goback="goback"></service>
	<phone  v-if="isshow_phone == true" :moblie="userinfo.username" @goback="goback" ></phone >
	<real  v-if="isshow_real == true" @goback="goback"></real >
	<agreement v-if="isshow_agreement == true" @goback="goback"></agreement>
	<payment v-if="isshow_payment == true"></payment>
	
	<view class="">
		<iframe :src="game_url" style="width: 100%;height: 100vh;" ref="gameIframe"></iframe>
    <button @click="sendMessageToIframe">向 iframe 发送消息</button>
	</view>
</template>

<script setup>
	import {
		onLoad,
		onShow
	} from "@dcloudio/uni-app";
	import {
		ref,
		onMounted,
		watch
	} from "vue";
	import nx from "@/nx";
	import userApi from "@/nx/api/user";
	import orderList from "../../components/orderList/orderList.vue";
	import recharge from "../../components/recharge/recharge.vue";
	import coupon from "../../components/coupon/coupon.vue";
	import gift from "../../components/gift/gift.vue";
	import outPop from "../../components/outPop/outPop.vue";
	import ball from "../../components/ball/ball.vue";
	import edition from "../../components/edition/edition.vue";
	import password from "../../components/password/password.vue";
	import service from "../../components/service/service.vue";
	import phone from "../../components/phone/phone.vue";
	import real from "../../components/real/real.vue";
	import agreement from "../../components/agreement/agreement.vue";
	import payment from "../../components/payment/payment.vue";
	const query = ref({
		device: "h5",
		channel_id: "100",
		appid: "",
		gameid: "",
		imeil: "",
		version: "v3.4.6",
	})
	const game_url = ref('')
	const isshow = ref(false)
	const isshow_edition = ref(false)
	const isshow_password = ref(false)
	const isshow_service = ref(false)
	const isshow_phone = ref(false)
	const isshow_real = ref(false)
	const isshow_agreement = ref(false)
	const isshow_payment = ref(false)
	const userinfo = ref({})
	const activeIndex = ref(0)
	const childPop = ref(null)
	const img_list = ref([{
			logo: "../../static/images/user/qimeng_ic_main_port_my_checked.png",
			logo1: "../../static/images/user/qimeng_ic_main_port_my_unchecked.png",
			name: "我的",
		},
		{
			logo: "../../static/images/user/qimeng_ic_main_port_running_water_checked.png",
			logo1: "../../static/images/user/qimeng_ic_main_port_running_water_unchecked.png",
			name: "订单",
		},
		{
			logo: "../../static/images/user/qimeng_ic_main_port_pay_checked.png",
			logo1: "../../static/images/user/qimeng_ic_main_port_pay_unchecked.png",
			name: "充值",
		},
		{
			logo: "../../static/images/user/qimeng_ic_main_port_voucher_checked.png",
			logo1: "../../static/images/user/qimeng_ic_main_port_voucher_unchecked.png",
			name: "代金券",
		},
		{
			logo: "../../static/images/user/qimeng_ic_main_port_gift_checked.png",
			logo1: "../../static/images/user/qimeng_ic_main_port_gift_unchecked.png",
			name: "礼包",
		},
	])
	const listClick = (index) => {
		activeIndex.value = index;
	}
	const togame = () =>{
		isshow.value = false
	}
	const navigateBack = () => {};
	const onClickItem = (item) => {
		if (item == "edition") {
			// isshow.value = false
			isshow_edition.value = true
		} else if (item == "password") {
			isshow_password.value = true
		} else if (item == "phone") {
			isshow_phone.value = true
		} else if (item == "real") {
			isshow_real.value = true
		} else if (item == "service") {
			isshow_service.value = true
		} else if (item == "loginout") {
			childPop.value.openPop()
		} else if (item == "agreement") {
			isshow_agreement.value = true
		}else if (item == 'download') {
			window.open('http://h5.176park.com/#/pages/invite/invite?cid=5cfc27444b42bdbc5d42e2552bdad8f7', '_blank')
			// window.location.href = 'http://h5.176park.com/#/pages/invite/invite?cid=5cfc27444b42bdbc5d42e2552bdad8f7'
		}
	};
	const userInfo = () => {
		userApi.userinfo(query.value).then((res) => {
			if (res.code === 1) {
				userinfo.value = res.data.userInfo
				uni.setStorageSync('userInfo', res.data.userInfo)
			}
		});
	};
	const outClick = () => {
		userApi.logout(query.value).then((res)=>{
			if(res.code==1) {
				uni.removeStorageSync('token')
				
				uni.navigateTo({
					url: "/pages/login/login?c=" + uni.getStorageSync('channel_id'),
				})
				uni.removeStorageSync('channel_id')
			}
		})
	}
	const updateParentParam = () =>{
		isshow.value = !isshow.value
		isshow_edition.value = false
		isshow_password.value = false
		isshow_service.value = false
		isshow_phone.value = false
		isshow_real.value = false
		isshow_agreement.value = false
		isshow_payment.value = false
	}
	const getGameUrl = () =>{
		query.value.code = uni.getStorageSync('channel_id')
		
		console.log("## query: ", query.value.code);
		userApi.getChannelInfo(query.value).then((res)=>{
			game_url.value = res.data.game_url
		})
	}
	const goback = () =>{
		isshow_phone.value = false
		isshow_edition.value = false
		isshow_password.value = false
		isshow_service.value = false
		isshow_real.value = false
		isshow_agreement.value = false
		userInfo()
	}

  const sendMessageToIframe = () => {
    const iframe = this.$refs.gameIframe;
    // 确保 iframe 已加载完成
    iframe.onload = () => {
      // 向 iframe 发送消息
      iframe.contentWindow.postMessage(
          { type: 'FROM_VUE', data: 'Hello from Vue!' },
          '*' // 目标域（* 表示任意域，建议替换为 iframe 的实际域名）
      );
    };
  }
	onMounted(() => {
		userInfo()
		if(!game_url.value) {
			getGameUrl()
		}
		
	});
	onLoad((options) => {
		console.log(options.initData);
		isshow.value = options.id
		activeIndex.value = options.num || 0
	
	})
</script>

<style lang="less" scoped>
	.mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1;

		.content {
			width: 100%;
			height: 88vh;
			position: fixed;
			bottom: 0;
			background-color: #fff;
			border-radius: 36rpx 36rpx 0 0;
		}

		.back {
			display: flex;
			align-items: center;
			margin: 32rpx 0 40rpx 32rpx;

			.icon {
				padding-top: 4rpx;
				margin-right: 8rpx;
			}
		}

		.user_logo {
			display: flex;
			align-items: center;
			margin: 0 20rpx 40rpx 24rpx;

			.logo {
				width: 120rpx;
				height: 120rpx;
				margin-right: 20rpx;
			}

			.user_name {
				font-size: 32rpx;
				color: #333;
			}

			.user_money {
				font-size: 20rpx;
				color: #f7a468;
				margin-top: 24rpx;

				image {
					width: 28rpx;
					height: 28rpx;
					vertical-align: top;
					margin-right: 8rpx;
				}
			}
		}

		.tabs {
			display: flex;
			justify-content: space-between;

			.tab {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 150rpx;

				.names {
					font-size: 28rpx;
					color: #333;
					margin-top: 12rpx;
				}

				.nameActive {
					font-size: 24rpx;
					color: #a6adc1;
				}
			}

			.active {
				border-bottom: 2rpx solid #486ce8;
				padding-bottom: 16rpx;
			}

			.img {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.line {
			width: 100%;
			height: 20rpx;
			background-color: #e9e9e9;
			margin-top: 52rpx;
		}

		.mytitle {
			margin: 32rpx 0 60rpx 32rpx;
			display: flex;
			align-items: center;

			.myleft {
				width: 8rpx;
				height: 28rpx;
				border-radius: 5rpx;
				background-color: #486ce8;
			}

			.myright {
				font-size: 28rpx;
				color: #333;
				margin-left: 8rpx;
			}
		}
		.sview {
			height: calc(100vh - 770rpx);
		}
		.mylist {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 0 32rpx;
			height: 90rpx;
			border-bottom: 1px solid #e9e9e9;
			font-size: 24rpx;

			.list_right {
				display: flex;
				align-items: center;
			}
		}
	}
</style>