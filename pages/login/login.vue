<template>
	<view class="mask">
		<!-- 账号登录 -->
		<view class="content" v-show="isshow==1">
			<image src="../../static/images/login/login_logo.png" mode="" class="img"></image>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_account_icon.png" mode=""></image>
				<input type="text" placeholder="请输入账号" v-model="query.username" />
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_password_icon.png" mode=""></image>
				<input type="text" placeholder="请输入6-15位密码" v-model="query.password" />
			</view>
			<view class="radio x-f">
				<view class="pay-left">
				  <image
				    v-if="agree == true"
				    src="../../static/images/user/qimeng_ic_unchecked.png"
				    @click="checkClick"
				  ></image>
				  <image
				    v-else
				    src="../../static/images/user/qimeng_ic_checked.png"
				    @click="checkClick"
				  ></image>
				</view>
				<view class="pay-right">
					<text class="agreement" @click="agreeClick(1)">《祈盟用户协议》</text>
					<text class="agreement" @click="agreeClick(2)">《隐私协议》</text>
				</view>
			</view>
			<view class="login_btn" @click="bindLogin" :class="{active_login: query.username&&query.password}">
				登录
			</view>
			<view class="login_bom">
				<view class="login_bom_left">
					<image src="../../static/images/login/qimeng_image_phone_login.png" mode=""></image>
					<text @click="phoneClick">手机注册</text>
				</view>
				<view class="login_bom_right">
					<text @click="pswClick">忘记密码</text>
					<text class="mid">|</text>
					<text @click="regClick">账号注册</text>
				</view>
			</view>
		</view>
		<!-- 账号注册 -->
		<view class="content content2" v-show="isshow==2">
			<image src="../../static/images/login/login_logo.png" mode="" class="img"></image>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_account_icon.png" mode=""></image>
				<input type="text" v-model="query.username" placeholder="请输入6-11位的账号" />
				<text class="users" @click="usernameClick">一键用户名</text>
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_password_icon.png" mode=""></image>
				<input type="text" v-model="query.password" placeholder="请输入6-15位密码" />
			</view>
			<view class="radio x-f">
				<view class="pay-left">
				  <image
				    v-if="agree == true"
				    src="../../static/images/user/qimeng_ic_unchecked.png"
				    @click="checkClick"
				  ></image>
				  <image
				    v-else
				    src="../../static/images/user/qimeng_ic_checked.png"
				    @click="checkClick"
				  ></image>
				</view>
				<view class="pay-right">
					<text class="agreement" @click="agreeClick(1)">《祈盟用户协议》</text>
					<text class="agreement" @click="agreeClick(2)">《隐私协议》</text>
				</view>
			</view>
			<view class="login_btn" @click="userRegister" :class="{active_login: query.username&&query.password}">
				注册
			</view>
			<view class="login_bom login_bom1">
				<view class="login_bom_left1">
					<text @click="toLogin">已有账号去登录</text>
					<uni-icons class="icon" type="right" size="18" color="#1E1E1E" @click="navigateBack"></uni-icons>
				</view>
				<!-- <view class="login_bom_right">
					<text>忘记密码</text>
					<text class="mid">|</text>
					<text>账号注册</text>
				</view> -->
			</view>
		</view>
		<!-- 手机注册 -->
		<view class="content content2" v-show="isshow==4">
			<image src="../../static/images/login/login_logo.png" mode="" class="img"></image>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_phone_icon.png" mode=""></image>
				<input type="text" placeholder="请输入手机号" v-model="query.username" />
				<!-- <text class="users">一键用户名</text> -->
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_verify_icon.png" mode=""></image>
				<input type="text" placeholder="请输入6-15位密码" v-model="query.code"  />
				<text class="users" :class="!countDownTimer? 'codeSend':'codeSend-gary'"  @click="getcode(register)">{{codeText}}</text>
			</view>
			<view class="radio x-f">
				<view class="pay-left">
				  <image
				    v-if="agree == true"
				    src="../../static/images/user/qimeng_ic_unchecked.png"
				    @click="checkClick"
				  ></image>
				  <image
				    v-else
				    src="../../static/images/user/qimeng_ic_checked.png"
				    @click="checkClick"
				  ></image>
				</view>
				<view class="pay-right">
					<text class="agreement" @click="agreeClick(1)">《祈盟用户协议》</text>
					<text class="agreement" @click="agreeClick(2)">《隐私协议》</text>
				</view>
			</view>
			<view class="login_btn" @click="nextClick" :class="{active_login: query.username&&query.code}">
				下一步
			</view>
			<view class="login_bom login_bom1">
				<view class="login_bom_left1">
					<text @click="toLogin">已有账号去登录</text>
					<uni-icons class="icon" type="right" size="18" color="#1E1E1E" @click="navigateBack"></uni-icons>
				</view>
			</view>
		</view>
		<!-- 找回密码 -->
		<view class="content content2" v-show="isshow == 5">
			<!-- <image src="../../static/images/login/login_logo.png" mode="" class="img"></image> -->
			<view class="pwd">找回密码</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_phone_icon.png" mode=""></image>
				<input type="text" placeholder="请输入手机号" v-model="query.username" />
				<!-- <text class="users">一键用户名</text> -->
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_verify_icon.png" mode=""></image>
				<input type="text" placeholder="请输入验证码" v-model="query.code" />
				<text class="users" :class="!countDownTimer? 'codeSend':'codeSend-gary'"  @click="getcode(password_find)">{{codeText}}</text>
			</view>
			<view class="pwd_title">该功能仅可用于，绑定过手机号的用户哦~</view>
			<view class="login_btn" @click="passwordClick" :class="{active_login: query.username&&query.code}">
				下一步
			</view>
			<view class="login_bom login_bom1">
				<view class="login_bom_left1">
					<text @click="toLogin">已有账号去登录</text>
					<uni-icons class="icon" type="right" size="18" color="#1E1E1E" @click="navigateBack"></uni-icons>
				</view>
			</view>
		</view>
		<!-- 设置新密码 -->
		<view class="content content2" v-show="isshow == 7">
			<!-- <image src="../../static/images/login/login_logo.png" mode="" class="img"></image> -->
			<view class="pwd">设置新密码</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_phone_icon.png" mode=""></image>
				<input type="text" placeholder="请输入6-15位密码" />
				<!-- <text class="users">一键用户名</text> -->
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_verify_icon.png" mode=""></image>
				<input type="text" placeholder="请重复登录密码" />
				<!-- <text class="users" >{{codeText}}</text> -->
			</view>
			<view class="pwd_title">该功能仅可用于，绑定过手机号的用户哦~</view>
			<view class="login_btn" @click="passwordClick">
				完成
			</view>
			<view class="login_bom login_bom1">
				<view class="login_bom_left1">
					<text @click="toLogin">已有账号去登录</text>
					<uni-icons class="icon" type="right" size="18" color="#1E1E1E" @click="navigateBack"></uni-icons>
				</view>
			</view>
		</view>
		<!-- 绑定手机号 -->
		<view class="content content2" v-show="isshow==8">
			<view class="bandphone">绑定手机号</view>
			<!-- <image src="../../static/images/login/login_logo.png" mode="" class="img"></image> -->
			<view class="username">
				<image src="../../static/images/login/qimeng_image_phone_icon.png" mode=""></image>
				<input type="text" placeholder="请输入手机号" v-model="query.username" />
				<!-- <text class="users">一键用户名</text> -->
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_verify_icon.png" mode=""></image>
				<input type="text" placeholder="请输入验证码" v-model="query.code"  />
				<text class="users" :class="!countDownTimer? 'codeSend':'codeSend-gary'"  @click="getcode('band')">{{codeText}}</text>
			</view>
		<!-- 	<view class="radio">
				<label>
					<radio activeBackgroundColor="#5868E1" value="r1" :checked="check" style="transform:scale(0.6)" />
					注册即代表同意
					<text class="agreement" @click="agreeClick(1)">《祈盟用户协议》</text>
					<text class="agreement" @click="agreeClick(2)">《隐私协议》</text>
				</label>
			</view> -->
			<view class="login_btn" @click="complete" :class="{active_login: query.username&&query.code}">
				完成绑定
			</view>
			<view class="font12 tiaoguo" @click="tiaoguo">
				跳过
			</view>
		</view>
		<!-- 隐私弹窗 -->
		<view class="content content1" v-show="isshow==3">
			<view class="title">
				<uni-icons class="icon" type="left" size="22" color="#1E1E1E" @click="navigateBack(tshow)"></uni-icons>
				<text class="title_h">{{type==2?'隐私协议':'祈盟用户协议'}}</text>
			</view>
			<scroll-view scroll-y="true" class="content_name">
				<view v-html="privacy"></view>
			</scroll-view>
		</view>
		<!-- 账号注册 -->
		<view class="content content2" v-show="isshow==6">
			<image src="../../static/images/login/login_logo.png" mode="" class="img"></image>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_account_icon.png" mode=""></image>
				<input type="text" v-model="query.password" placeholder="请输入6-15位密码" />
				<!-- <text class="users" @click="usernameClick">一键用户名</text> -->
			</view>
			<view class="username">
				<image src="../../static/images/login/qimeng_image_password_icon.png" mode=""></image>
				<input type="text" v-model="query.password1" placeholder="请输入6-15位密码" />
			</view>
			<view class="radio">
				<label>
					<radio activeBackgroundColor="#5868E1" value="r1" style="transform:scale(0.6)" />注册即代表同意
					<text class="agreement" @click="agreeClick(1)">《祈盟用户协议》</text>
					<text class="agreement" @click="agreeClick(2)">《隐私协议》</text>
				</label>
			</view>
			<view class="login_btn" @click="register">
				注册
			</view>
			<view class="login_bom login_bom1">
				<view class="login_bom_left1">
					<text @click="toLogin">已有账号去登录</text>
					<uni-icons class="icon" type="right" size="18" color="#1E1E1E" @click="navigateBack"></uni-icons>
				</view>
				<!-- <view class="login_bom_right">
					<text>忘记密码</text>
					<text class="mid">|</text>
					<text>账号注册</text>
				</view> -->
			</view>
		</view>
		<!-- 	<view class="agreenment">
			<navigator url="/pages/login/reg" open-type="navigate">注册账户</navigator>
		</view> -->
		<codePop ref="childPop" :imgs="img" @codeClick="getcode1" @submit="submit"></codePop>
		
	</view>
	<!-- <view class="">
		<iframe src="http://devsdkapi.qmgames.cn/game_h5_demo.html"></iframe>
	</view> -->
</template>

<script setup>
	import {
		ref,
		onMounted,
		customRef
	} from 'vue';
	import nx from '@/nx';
	import userApi from '@/nx/api/user';
	import codePop from "../../components/codePop/codePop.vue";
	import {
		onLoad,
		onHide,
		onShow,
		onUnload,
		onBackPress
	} from '@dcloudio/uni-app';
	const privacy = ref('')
	const isshow = ref(1)
	const tshow = ref(1)
	const type = ref(1)
	const agree = ref(true)
	const childPop = ref(null)
	const codeText = ref('获取验证码')
	const countDown = ref(60)
	const countDownTimer = ref(null)
	const channel_id = ref('')
	const img = ref(
		'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAABLBAMAAAA41acQAAAAIVBMVEXz+/6CMX3k4e3WyN2eY52sfK3Ir826lr2QSo2MQ4m2j7k9xrQWAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAD9UlEQVRoge1ZPVfbMBSVEjCMUhOCxwBtYCT0QDvGS7uS0xYYkw4dOvHZw4h7ymnW/uP6PflDsp8jOXFSTo/vgtGHdXXv05OsMNagQYMGDRo0aNCgQYMG/zO4FNY2B2vgYYILaecV7HxdBxcNQnIpLW3aUsqP/XWwScA5yGVptBHRktdr4RNDCJArCjAFutEQqvq1jtva3397zFpnx2Qt5ygXY9IK+8KoAjBg1A5KPBAA0IhbaZXhy11/AVrbUc/zsZQTshbVwqeFac3BHIFBhgdZEhoc5UrbrRmBLAtmkYi1Grms6JawYiKn9HpV26EjC4jZkGWt+mnt0mo5sCrNWsOlSe18uyVZgVxWWgE9q1aIr/799h7/mnkxTErmsfKJuQKKkUUANkT5VCzfSF2YwsPIoAwlPZ19jgU2eFXKilcJLRPDVCSvMIaX0TpCtx5CkznOtWwrFi4ewvA+UY4qdPAxIxFjMyM6Vjp5aWPEFtKiB3dihcP3isUoSKwiNDGW+nZqK8rSPUZ6WhPg3S2h5ZQevHmh1c2YG7T2oHKSjB/hAgLQN3vvctJFJ1Zq4hOid+Yhxq9Ba5oan2TnHXOngeIeTcvNxGwEE3vZ8ttKlmSKseI5uLoe6hnB7N1hpItLhZZaiFFotW6VoJ18p87gKnIZmonTME9riEuCpOUeWiOiIg6f07CH2hgjYJz7yOQPRmb7UYtEwBSLKRcrhBZ1ssWgmUQR3VU56DytObtPM6hM88C70PB5rCZLBZeTiTey5PSBK3EElFC3ZNMscEpWQ+tRz7gBTiQ6ry/GSkUJVeGpOA/1oQcEJ8Cng+Cizw61rSKczWYTRu19y4VWvNd9COKRn5jlSNGlPoAlwcAltNhgTIdWvEGn6LNkozPgX35PnyfFdxRN5MzJRMZOn+nyTX145fNRTqDLk6jwdfwfdeIjlqJwY1WOG42BErQVZCX+xUkyL8WR1LyQuQTItRS8LL5/xkVKQcxY2mY0+Bz6JVcYBVqOFs7Dm4RV5k+kYPfHPpRRO0MReRfdkqkFh0qv3X5a0v51Rx0LnWk5pi0L2lfAIP8ezLRPbm/Ida6FVYSDcdEvTPuO9045WrWYCBgWv0Gn2dq0wnSxNlawl+ePY5gmHLvngqsuE6MzoJ//BsU05drfcLE2Vqz9nLfLK8vpdlr1mVgE5nry1EFBd3GVrFSud/ZCpyXqM5HCTclhiIQ2A6crkcXhVbkuz2hxtxukxfHeNW0xw0WXK5Fl0JpVaJzSIu4ma4ZXoW3iotvd5NqQnJzFiiOrKmIX+YoXYlUoufA3gn9NxQCw4UL7jeBlAOQScuXrsDKi3YrzFxbxDO9Z4HfNfPFfuK6r2BXQe4YAAAAASUVORK5CYII='
		)
	const query = ref({
		device: '4',
		channel_id: '100',
		appid: '30',
		gameid: '30',
		imeil: '17322362573792438064',
		version: 'v3.4.6'
	})
	  
	const agreeClick = (id) => {
		privacy.value = ''
		isshow.value = 3
		type.value = id
		userApi.privacy(query.value).then((res)=>{
			if(id==1) {
				privacy.value = res.data.user_agreement

			}else {
				privacy.value = res.data.privacy_agreement

			}
		})
	}
	//返回按钮
	const navigateBack = (id) => {
		isshow.value = id
	}
	//注册
	const regClick = () => {
		query.value.username = ''
		query.value.password = ''
		isshow.value = 2
		tshow.value = 2
		agree.value = true
	}
	//去登录
	const toLogin = () => {
		query.value.username = ''
		query.value.password = ''
		isshow.value = 1
		tshow.value = 1
	}
	//手机注册
	const phoneClick = () => {
		query.value.username = ''
		isshow.value = 4
		tshow.value = 4
		agree.value = true
	}
	//找回密码
	const pswClick = () => {
		
		isshow.value = 5
	}
	//tioaguo
	const tiaoguo = () =>{
		uni.navigateTo({
			url: '/pages/certification/certification'
		})
	}
	const login = () => {
		uni.getSystemInfo({
			success: function(info) {
				console.log('设备UUID:', info.deviceId);
			}
		});
	}
	const bindLogin = () => {
		if(!query.value.username) {
			uni.showToast({
				title: '请输入正确账号！',
				icon: 'none'
			})
			return
		}
		if(!query.value.password) {
			uni.showToast({
				title: '请输入正确密码！',
				icon: 'none'
			})
			return
		}
		if(agree.value == true) {
			uni.showToast({
				title: '请勾选隐私协议',
				icon: 'none'
			})
			return
		}
	
		// 提交数据
		query.value.channel_id = channel_id.value
		userApi.accountLogin(query.value).then((res) => {
			if (res.code === 1) {
				uni.showToast({
					title: '登录成功！',
					icon: 'none'
				})

				uni.setStorageSync('token', res.data.token)
                userApi.isVerified(query.value).then((res)=>{
					if(res.data.isVerified==1) {
						// 跳转首页
						uni.navigateTo({
							url: '/pages/index/index'
						})
					}else {
						uni.navigateTo({
							url: '/pages/certification/certification'
						})
					}
				})
				
			}
		})
	}
	const submit = ()=>{
		onSendCode()
	}
	//发送验证码
	const onSendCode = () => {
		if (countDownTimer.value) {
			return;
		}
		if(isshow.value == 8) {
			userApi.getBindPhoneCode({
				captcha:childPop.value.nickname,
				phone: query.value.username,
				random:'eb55f905cbd4220b3c06b0e995a441d41',
				type:1,
				device: '4',
				channel_id: '100',
				appid: '231',
				gameid: '30',
				imeil: '17322362573792438064',
				version: 'v3.4.6'
			}).then((res) => {
				if (res.code==1) {
					childPop.value.onPopClose();
					countDown.value = 60
					startCountdownTimer()
				}
			}).catch(res => {
				console.log(res)
			})
		}else {
			userApi.send_sms({
				appid:'231',
				mobile: query.value.username,
				action: 'register',
				captcha: childPop.value.nickname,
				random:'eb55f905cbd4220b3c06b0e995a441d41'
			}).then((res) => {
				if (res.code==1) {
					
					countDown.value = 60
					startCountdownTimer()
				}
				childPop.value.onPopClose();
			}).catch(res => {
				console.log(res)
			})
		}
		
	
	}
	const startCountdownTimer = () => {
		countDownTimer.value = setInterval(() => {
			if (countDown.value > 0) {
				countDown.value--;
				codeText.value = `${countDown.value}秒后重新获取`
			} else {
				resetCountDown();
	
			}
		}, 1000)
	}
	const resetCountDown = () => {
		clearInterval(countDownTimer.value)
		countDownTimer.value = null
		codeText.value = '获取验证码'
	}
	const getcode = (id) => {
		childPop.value.openPop();
		query.value.type = 1
		if(id !='band') {
			query.value.action= id
		}
		
		userApi.captcha(query.value).then((res) => {
			img.value =  res.data.img_str
			// console.log(res);
		})
	}
	const usernameClick = () => {
		userApi.createUsername(query.value).then((res) => {
			query.value.username = res.data.username
		})
	}
	const nextClick = () =>{
		if(!query.value.username) {
			uni.showToast({
				title: '请输入正确手机号码！',
				icon: 'none'
			})
			return
		}
		if(!query.value.code) {
			uni.showToast({
				title: '请输入正确验证码！',
				icon: 'none'
			})
			return
		}
		if(agree.value == true) {
			uni.showToast({
				title: '请勾选隐私政策！',
				icon: 'none'
			})
			return
		}
		
		isshow.value = 6
	}
	//手机号注册
	const register = () =>{
		userApi.register({device: 4,
		channel_id: '100',
		appid: '30',
		gameid: '30',
		imeil: '17322362573792438064',
		version: 'v3.4.6',password:query.value.password,phone_brand:'HUAWEI',phone_model:'GLK-AL00',type:'mobile',username:query.value.username,sms_code:query.value.code}).then((res)=>{
			uni.setStorageSync('token', res.data.token)
			uni.navigateTo({
				url: '/pages/certification/certification'
			})
		})
	}
	//账号注册
	const userRegister = () =>{
		if(!query.value.username) {
			uni.showToast({
				title: '请输入正确账号！',
				icon: 'none'
			})
			return
		}
		if(!query.value.password) {
			uni.showToast({
				title: '请输入密码！',
				icon: 'none'
			})
			return
		}
		if(agree.value == true) {
			uni.showToast({
				title: '请勾选隐私政策！',
				icon: 'none'
			})
			return
		}
		userApi.register({device: 4,
		channel_id: '100',
		appid: '30',
		gameid: '30',
		imeil: '17322362573792438064',
		version: 'v3.4.6',password:query.value.password,phone_brand:'HUAWEI',phone_model:'GLK-AL00',username:query.value.username}).then((res)=>{
			if(res.code == 1) {
				uni.setStorageSync('token', res.data.token)
				isshow.value = 8
				query.value.username = ''
			}else {
				
			}
			
			// uni.navigateTo({
			// 	url: '/pages/certification/certification'
			// })
		})
	}
	//完成绑定
	const complete = () =>{
		query.value.type =1
		query.value.phone = query.value.username
		query.value.username= ''
		console.log(query.value);
		userApi.bindPhone(query.value).then((res)=>{
			if(res.code == 1) {
				uni.navigateTo({
					url: '/pages/certification/certification'
				})
			}else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
				return
			}
			
		})
		
	}
	//找回密码
	const passwordClick = ()=>{
		if(!query.value.username) {
			uni.showToast({
				title: '请输入正确手机号码！',
				icon: 'none'
			})
			return
		}
		if(!query.value.code) {
			uni.showToast({
				title: '请输入正确验证码！',
				icon: 'none'
			})
			return
		}
		isshow.value=7
	}
	const radioChange = (e) =>{
		// console.log(e)
		check.value = true
	}
	const checkClick = () => {
		agree.value = !agree.value
	}
	onLoad((options) => {
	  // options 包含路由传递的参数
	 query.value.code = options.id
	 uni.setStorageSync('channel_id', options.c)
	 // userApi.getChannelInfo(query.value).then((res)=>{
		//  channel_id.value = res.data.channel_id
		//  console.log(res,channel_id.value)
	 // })
	})
</script>

<style lang="less" scoped>
	input {
		font-size: 12px;
	}

	.mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1;

		.content {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 584rpx;
			// height: 500rpx;
			background-color: #fff;
			border-radius: 26rpx;

			.img {
				width: 261rpx;
				height: 65rpx;
				margin: 36rpx 166rpx 36rpx;
			}

			.username {
				width: 508rpx;
				height: 64rpx;
				background-color: #F5F5F5;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				border-radius: 32rpx;
				margin: 0 0 20rpx 36rpx;

				image {
					width: 36rpx;
					height: 36rpx;
					margin: 0 12px;
				}
			}

			.radio {
				font-size: 20rpx;
				color: #333;
				margin-left: 36rpx;
				image {
					width: 40rpx;
					height: 40rpx;
					vertical-align: middle;
				}
				.agreement {
					color: #4F68E1;
				}
			}

			.login_btn {
				text-align: center;
				width: 508rpx;
				height: 72rpx;
				line-height: 72rpx;
				background-color: #A2AFFD;
				color: #fff;
				font-size: 28rpx;
				border-radius: 36rpx;
				margin: 20rpx 0 20rpx 36rpx;
			}
			.active_login {
				background-color: #4F68E1;
			}

			.login_bom {
				display: flex;
				justify-content: space-between;
				margin: 20rpx 36rpx 40rpx;

				.login_bom_left {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					color: #5868E1;

					image {
						width: 40rpx;
						height: 40rpx;
					}
				}

				.login_bom_right {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					color: #999;

					.mid {
						margin: 0 12rpx;
					}
				}
			}
			.tiaoguo {
				margin: 32rpx;
				color: #5868E1;
			}
		}

		.content1 {
			height: 570rpx;

			// margin: 0 32rpx;
			.icon {
				position: absolute;
				top: 32rpx;
				left: 24rpx;
				// margin: 32rpx 160rpx 0 32rpx;
			}

			.title {
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.title_h {
				margin: 32rpx;
			}

			.content_name {
				width: 512rpx;
				margin: 0 32rpx;
				line-height: 42rpx;
				max-height: 450rpx;

			}
		}

		.content2 {
			
			.codeSend {
				font-size: 26rpx;
				color: #05D4BB;
			}
			
			.bandphone {
				text-align: center;
				margin: 24rpx;
			}
			.users {
				position: absolute;
				right: 68rpx;
				font-size: 12px;
				color: #5868E1;
			}
			.codeSend-gary {
				font-size: 26rpx;
				color: #999999;
			}
			.login_bom1 {
				justify-content: flex-end;
			}

			.login_bom_left1 {
				display: flex;
				align-items: center;
				font-size: 14px;

			}

			.pwd {
				text-align: center;
				font-size: 16px;
				color: #000;
				margin: 32rpx 0 48rpx;
			}

			.pwd_title {
				font-size: 10px;
				color: #000;
				margin: 0 0 36rpx 36rpx;
			}
		}
	}
</style>