<template>
	<view class="demo-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="navigateBack">
					<text class="back-icon">&lt;</text>
				</view>
				<view class="navbar-title">Demo演示</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="content-wrapper">
			<!-- 控制按钮区域 -->
			<view class="button-section">
				<view class="section-title">iframe通信控制</view>
				<view class="button-group">
					<button class="demo-btn primary" @click="sendMessageToIframe">
						发送消息到iframe
					</button>
					<button class="demo-btn secondary" @click="requestDataFromIframe">
						请求iframe数据
					</button>
					<button class="demo-btn" @click="clearMessages">
						清空消息记录
					</button>
				</view>
			</view>

			<!-- 消息显示区域 -->
			<view class="message-section" v-if="messages.length > 0">
				<view class="section-title">通信记录</view>
				<view class="message-list">
					<view
						class="message-item"
						v-for="(message, index) in messages"
						:key="index"
						:class="message.type"
					>
						<view class="message-time">{{ message.time }}</view>
						<view class="message-content">{{ message.content }}</view>
					</view>
				</view>
			</view>

			<!-- iframe区域 -->
			<view class="iframe-section">
				<view class="section-title">
					iframe内容
					<text class="iframe-url">{{ iframeUrl }}</text>
				</view>
				<view class="iframe-container">
					<web-view
						:src="iframeUrl"
						@message="handleIframeMessage"
						@load="handleIframeLoad"
						@error="handleIframeError"
					></web-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, onMounted, onUnmounted } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'

	// 响应式数据
	const iframeUrl = ref('/static/iframe-demo.html') // 默认iframe地址，您可以修改为需要的地址
	const messages = ref([])
	const messageCounter = ref(0)

	// iframe相关状态
	const iframeLoaded = ref(false)
	const webViewContext = ref(null)

	// 添加消息到记录
	const addMessage = (content, type = 'info') => {
		const now = new Date()
		const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

		messages.value.unshift({
			id: ++messageCounter.value,
			content,
			type,
			time
		})

		// 限制消息数量，避免内存占用过多
		if (messages.value.length > 50) {
			messages.value = messages.value.slice(0, 50)
		}
	}

	// 发送消息到iframe
	const sendMessageToIframe = () => {
		if (!iframeLoaded.value) {
			uni.showToast({
				title: 'iframe还未加载完成',
				icon: 'none'
			})
			return
		}

		const messageData = {
			type: 'greeting',
			data: {
				message: 'Hello from uni-app!',
				timestamp: Date.now(),
				source: 'uni-app-demo'
			}
		}

		try {
			// 注意：uni-app的web-view组件在不同平台的postMessage实现可能有差异
			// 这里使用uni-app推荐的方式
			uni.postMessage({
				data: messageData
			})

			addMessage(`发送消息: ${JSON.stringify(messageData)}`, 'sent')
		} catch (error) {
			addMessage(`发送失败: ${error.message}`, 'error')
		}
	}

	// 请求iframe数据
	const requestDataFromIframe = () => {
		if (!iframeLoaded.value) {
			uni.showToast({
				title: 'iframe还未加载完成',
				icon: 'none'
			})
			return
		}

		const requestData = {
			type: 'dataRequest',
			data: {
				requestId: `req_${Date.now()}`,
				requestType: 'getUserInfo'
			}
		}

		try {
			uni.postMessage({
				data: requestData
			})

			addMessage(`请求数据: ${JSON.stringify(requestData)}`, 'sent')
		} catch (error) {
			addMessage(`请求失败: ${error.message}`, 'error')
		}
	}

	// 处理来自iframe的消息
	const handleIframeMessage = (event) => {
		console.log('收到iframe消息:', event)

		try {
			const messageData = event.detail.data[0] || event.detail.data
			addMessage(`收到消息: ${JSON.stringify(messageData)}`, 'received')
		} catch (error) {
			addMessage(`消息解析失败: ${error.message}`, 'error')
		}
	}

	// iframe加载完成
	const handleIframeLoad = (event) => {
		console.log('iframe加载完成:', event)
		iframeLoaded.value = true
		addMessage('iframe加载完成', 'success')
	}

	// iframe加载错误
	const handleIframeError = (event) => {
		console.log('iframe加载错误:', event)
		iframeLoaded.value = false
		addMessage(`iframe加载错误: ${event.detail.errMsg || '未知错误'}`, 'error')
	}

	// 清空消息记录
	const clearMessages = () => {
		messages.value = []
		uni.showToast({
			title: '消息记录已清空',
			icon: 'success'
		})
	}

	// 返回上一页
	const navigateBack = () => {
		uni.navigateBack({
			delta: 1
		})
	}

	// 页面加载时的处理
	onLoad((options) => {
		console.log('Demo页面加载，参数:', options)

		// 如果通过参数传递了iframe地址，则使用传递的地址
		if (options.url) {
			iframeUrl.value = decodeURIComponent(options.url)
		}

		addMessage('Demo页面初始化完成', 'info')
	})

	// 组件挂载时
	onMounted(() => {
		console.log('Demo组件挂载完成')
	})

	// 组件卸载时
	onUnmounted(() => {
		console.log('Demo组件卸载')
	})
</script>

<style lang="less" scoped>
	.demo-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	// 自定义导航栏
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background-color: #fff;
		border-bottom: 1px solid #e5e5e5;

		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 88rpx;
			padding: 0 32rpx;
			padding-top: var(--status-bar-height, 44rpx);

			.navbar-left {
				width: 80rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				cursor: pointer;

				.back-icon {
					font-size: 48rpx;
					font-weight: 300;
					color: #333;
					line-height: 1;
					transform: scale(1.2);
					transition: all 0.2s ease;
				}

				&:active .back-icon {
					color: #5868E1;
					transform: scale(1.1);
				}
			}

			.navbar-title {
				flex: 1;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}

			.navbar-right {
				width: 80rpx;
			}
		}
	}

	// 内容区域
	.content-wrapper {
		padding-top: calc(88rpx + var(--status-bar-height, 44rpx));
		padding: calc(88rpx + var(--status-bar-height, 44rpx)) 32rpx 32rpx;
	}

	// 通用区块样式
	.section-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 24rpx;
		padding-left: 16rpx;
		border-left: 6rpx solid #5868E1;
	}

	// 按钮区域
	.button-section {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

		.button-group {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
		}

		.demo-btn {
			height: 80rpx;
			border-radius: 12rpx;
			font-size: 28rpx;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&.primary {
				background-color: #5868E1;
				color: #fff;

				&:active {
					background-color: #4F68E1;
				}
			}

			&.secondary {
				background-color: #A2AFFD;
				color: #fff;

				&:active {
					background-color: #8FA2FC;
				}
			}

			&:not(.primary):not(.secondary) {
				background-color: #f0f0f0;
				color: #666;

				&:active {
					background-color: #e0e0e0;
				}
			}
		}
	}

	// 消息区域
	.message-section {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

		.message-list {
			max-height: 400rpx;
			overflow-y: auto;
		}

		.message-item {
			padding: 16rpx 20rpx;
			margin-bottom: 16rpx;
			border-radius: 12rpx;
			border-left: 6rpx solid #ddd;

			&.sent {
				background-color: #e8f4fd;
				border-left-color: #5868E1;
			}

			&.received {
				background-color: #f0f9ff;
				border-left-color: #10b981;
			}

			&.success {
				background-color: #f0fdf4;
				border-left-color: #22c55e;
			}

			&.error {
				background-color: #fef2f2;
				border-left-color: #ef4444;
			}

			&.info {
				background-color: #f8fafc;
				border-left-color: #64748b;
			}

			.message-time {
				font-size: 20rpx;
				color: #999;
				margin-bottom: 8rpx;
			}

			.message-content {
				font-size: 24rpx;
				color: #333;
				word-break: break-all;
				line-height: 1.4;
			}
		}
	}

	// iframe区域
	.iframe-section {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

		.iframe-url {
			font-size: 20rpx;
			color: #999;
			margin-left: 16rpx;
		}

		.iframe-container {
			height: 600rpx;
			border-radius: 12rpx;
			overflow: hidden;
			border: 2rpx solid #e5e5e5;

			web-view {
				width: 100%;
				height: 100%;
			}
		}
	}

	// 滚动条样式（仅在支持的平台生效）
	::-webkit-scrollbar {
		width: 8rpx;
	}

	::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 4rpx;
	}

	::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 4rpx;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}
</style>