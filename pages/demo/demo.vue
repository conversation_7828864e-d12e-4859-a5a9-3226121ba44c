<template>
	<view class="demo-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="navigateBack">
					<view class="back-arrow"></view>
				</view>
				<view class="navbar-title">Demo演示</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="content-wrapper">
			<!-- 控制按钮区域 -->
			<view class="button-section">
				<view class="section-title">iframe通信控制</view>
				<view class="button-group">
					<button class="demo-btn primary" @click="sendMessageToIframe">
						发送消息到iframe
					</button>
					<button class="demo-btn secondary" @click="requestDataFromIframe">
						请求iframe数据
					</button>
					<button class="demo-btn" @click="clearMessages">
						清空消息记录
					</button>
				</view>
			</view>

			<!-- 消息显示区域 -->
			<view class="message-section" v-if="messages.length > 0">
				<view class="section-title">通信记录</view>
				<view class="message-list">
					<view 
						class="message-item" 
						v-for="(message, index) in messages" 
						:key="index"
						:class="message.type"
					>
						<view class="message-time">{{ message.time }}</view>
						<view class="message-content">{{ message.content }}</view>
					</view>
				</view>
			</view>

			<!-- iframe区域 -->
			<view class="iframe-section">
				<view class="section-title">
					iframe内容
					<text class="iframe-url">{{ iframeUrl }}</text>
				</view>
				<view class="iframe-container">
					<web-view 
						:src="iframeUrl" 
						@message="handleIframeMessage"
						@load="handleIframeLoad"
						@error="handleIframeError"
					></web-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted, onUnmounted } from 'vue'
	import { onLoad } from '@dcloudio/uni-app'

	// 响应式数据
	const iframeUrl = ref('/static/iframe-demo.html')
	const messages = ref([])
	const messageCounter = ref(0)
	const iframeLoaded = ref(false)

	// 添加消息到记录
	const addMessage = (content, type = 'info') => {
		const now = new Date()
		const time = now.getHours().toString().padStart(2, '0') + ':' + 
					 now.getMinutes().toString().padStart(2, '0') + ':' + 
					 now.getSeconds().toString().padStart(2, '0')
		
		messages.value.unshift({
			id: ++messageCounter.value,
			content,
			type,
			time
		})

		if (messages.value.length > 50) {
			messages.value = messages.value.slice(0, 50)
		}
	}

	// 发送消息到iframe
	const sendMessageToIframe = () => {
		if (!iframeLoaded.value) {
			uni.showToast({
				title: 'iframe还未加载完成',
				icon: 'none'
			})
			return
		}

		const messageData = {
			type: 'greeting',
			data: {
				message: 'Hello from uni-app!',
				timestamp: Date.now(),
				source: 'uni-app-demo'
			}
		}

		try {
			uni.postMessage({
				data: messageData
			})
			
			addMessage('发送消息: ' + JSON.stringify(messageData), 'sent')
		} catch (error) {
			addMessage('发送失败: ' + error.message, 'error')
		}
	}

	// 请求iframe数据
	const requestDataFromIframe = () => {
		if (!iframeLoaded.value) {
			uni.showToast({
				title: 'iframe还未加载完成',
				icon: 'none'
			})
			return
		}

		const requestData = {
			type: 'dataRequest',
			data: {
				requestId: 'req_' + Date.now(),
				requestType: 'getUserInfo'
			}
		}

		try {
			uni.postMessage({
				data: requestData
			})
			
			addMessage('请求数据: ' + JSON.stringify(requestData), 'sent')
		} catch (error) {
			addMessage('请求失败: ' + error.message, 'error')
		}
	}

	// 处理来自iframe的消息
	const handleIframeMessage = (event) => {
		console.log('收到iframe消息:', event)
		
		try {
			const messageData = event.detail.data[0] || event.detail.data
			addMessage('收到消息: ' + JSON.stringify(messageData), 'received')
		} catch (error) {
			addMessage('消息解析失败: ' + error.message, 'error')
		}
	}

	// iframe加载完成
	const handleIframeLoad = (event) => {
		console.log('iframe加载完成:', event)
		iframeLoaded.value = true
		addMessage('iframe加载完成', 'success')
	}

	// iframe加载错误
	const handleIframeError = (event) => {
		console.log('iframe加载错误:', event)
		iframeLoaded.value = false
		addMessage('iframe加载错误: ' + (event.detail.errMsg || '未知错误'), 'error')
	}

	// 清空消息记录
	const clearMessages = () => {
		messages.value = []
		uni.showToast({
			title: '消息记录已清空',
			icon: 'success'
		})
	}

	// 返回上一页
	const navigateBack = () => {
		uni.navigateBack({
			delta: 1
		})
	}

	// 页面加载时的处理
	onLoad((options) => {
		console.log('Demo页面加载，参数:', options)
		
		if (options.url) {
			iframeUrl.value = decodeURIComponent(options.url)
		}
		
		addMessage('Demo页面初始化完成', 'info')
	})

	onMounted(() => {
		console.log('Demo组件挂载完成')
	})

	onUnmounted(() => {
		console.log('Demo组件卸载')
	})
</script>
