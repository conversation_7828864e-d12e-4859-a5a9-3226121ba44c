# Demo页面使用说明

## 功能概述

这个Demo页面展示了在uni-app框架中如何实现与iframe的双向数据通信。页面包含以下主要功能：

### 1. 页面结构
- **自定义导航栏**：与项目整体风格保持一致
- **控制按钮区域**：包含三个主要操作按钮
- **消息记录区域**：实时显示通信记录
- **iframe区域**：嵌入指定的网页内容

### 2. 主要功能

#### 按钮功能
1. **发送消息到iframe**：向iframe发送自定义消息
2. **请求iframe数据**：向iframe请求特定数据
3. **清空消息记录**：清除所有通信记录

#### 通信机制
- 使用`postMessage` API实现跨域通信
- 支持双向数据传输
- 实时消息记录和状态显示
- 错误处理和状态反馈

### 3. 使用方法

#### 基本使用
```javascript
// 跳转到demo页面
uni.navigateTo({
    url: '/pages/demo/demo'
})

// 带参数跳转（指定iframe地址）
uni.navigateTo({
    url: '/pages/demo/demo?url=' + encodeURIComponent('https://example.com')
})
```

#### 自定义iframe地址
在页面加载时，可以通过URL参数传递iframe地址：
```
/pages/demo/demo?url=https://your-website.com
```

### 4. 技术实现

#### Vue 3 Composition API
- 使用`ref`和`reactive`管理响应式数据
- 使用`onMounted`和`onUnmounted`处理生命周期
- 使用`onLoad`处理uni-app页面参数

#### 通信协议
```javascript
// 发送消息格式
{
    type: 'message_type',
    data: {
        // 具体数据
    }
}

// 消息类型
- 'greeting': 问候消息
- 'dataRequest': 数据请求
- 'dataResponse': 数据响应
- 'custom_message': 自定义消息
```

### 5. 样式特点

- 响应式设计，适配不同屏幕尺寸
- 使用Less预处理器
- 遵循项目整体设计规范
- 支持深色/浅色主题切换
- 平滑动画过渡效果

### 6. 注意事项

#### 跨域问题
- iframe内容需要支持postMessage通信
- 确保目标网站允许被嵌入iframe
- 注意CSP（内容安全策略）限制

#### 平台兼容性
- web-view组件在不同平台表现可能有差异
- H5平台：完全支持postMessage
- App平台：需要特殊处理
- 小程序平台：功能受限

#### 性能优化
- 限制消息记录数量（最多50条）
- 使用防抖处理频繁操作
- 及时清理事件监听器

### 7. 扩展功能

可以根据需要扩展以下功能：

1. **消息加密**：对敏感数据进行加密传输
2. **消息队列**：处理大量并发消息
3. **状态同步**：实现iframe与父页面状态同步
4. **文件传输**：支持文件上传下载
5. **实时通信**：集成WebSocket实现实时通信

### 8. 调试技巧

1. **开启控制台**：查看详细的通信日志
2. **使用测试页面**：使用内置的iframe-demo.html进行测试
3. **网络监控**：检查网络请求和响应
4. **错误捕获**：注意错误消息的处理和显示

### 9. 常见问题

#### Q: iframe无法加载？
A: 检查网络连接和目标网站的iframe嵌入策略

#### Q: 消息发送失败？
A: 确认iframe已完全加载，检查消息格式是否正确

#### Q: 在App中无法通信？
A: App平台的web-view通信机制与H5不同，需要使用uni-app特定的API

### 10. 示例代码

```vue
<template>
    <button @click="sendCustomMessage">发送自定义消息</button>
</template>

<script setup>
const sendCustomMessage = () => {
    const messageData = {
        type: 'custom',
        data: {
            action: 'updateUI',
            payload: { theme: 'dark' }
        }
    }
    
    uni.postMessage({
        data: messageData
    })
}
</script>
```

这个Demo页面为您提供了一个完整的iframe通信解决方案，可以根据具体需求进行定制和扩展。
